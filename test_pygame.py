#!/usr/bin/env python3
"""
Simple pygame test to check if pygame is working
"""
import sys

print("Python version:", sys.version)
print("Testing pygame import...")

try:
    import pygame
    print("✓ Pygame imported successfully")
    print("Pygame version:", pygame.version.ver)
except ImportError as e:
    print("✗ Failed to import pygame:", e)
    print("Please install pygame with: pip install pygame")
    sys.exit(1)

print("Testing pygame initialization...")
try:
    pygame.init()
    print("✓ Pygame initialized successfully")
except Exception as e:
    print("✗ Failed to initialize pygame:", e)
    sys.exit(1)

print("Testing display creation...")
try:
    # Try to create a small test window
    screen = pygame.display.set_mode((400, 300))
    pygame.display.set_caption("Pygame Test")
    print("✓ Display created successfully")
    
    # Fill with a color and update
    screen.fill((0, 128, 255))  # Blue background
    pygame.display.flip()
    print("✓ Display updated successfully")
    
    # Keep window open for 2 seconds
    pygame.time.wait(2000)
    
    pygame.quit()
    print("✓ Pygame quit successfully")
    print("All tests passed! Pygame is working correctly.")
    
except Exception as e:
    print("✗ Display test failed:", e)
    print("This might be due to:")
    print("  - No display available (headless system)")
    print("  - Graphics driver issues")
    print("  - Display server not running")
    pygame.quit()
    sys.exit(1)
