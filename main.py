import pygame
import sys
import os
import random
import time

# --- CONFIG ---
WIDTH, HEIGHT = 800, 600
FPS = 60
ASSETS = os.path.join(os.path.dirname(__file__), 'assets')

# UI Palette
PALETTE = [
    (208, 129, 89),   # #d08159
    (255, 170, 94),   # #ffaa5e
    (255, 212, 163),  # #ffd4a3
    (255, 236, 214),  # #ffecd6
]

# Nacho States
NACHO_HAPPY = 0
NACHO_ANXIOUS = 1
NACHO_TERRIFIED = 2
NACHO_CRACKED = 3
NACHO_CONFIDENT = 4

# Game States
STATE_TITLE = 0
STATE_PLAYING = 1
STATE_GAMEOVER = 2

# --- UTILS ---
def load_sprite_sheet(path, rows, cols):
    sheet = pygame.image.load(path).convert_alpha()
    w, h = sheet.get_width() // cols, sheet.get_height() // rows
    sprites = []
    for y in range(rows):
        for x in range(cols):
            rect = pygame.Rect(x * w, y * h, w, h)
            sprites.append(sheet.subsurface(rect))
    return sprites, w, h

def draw_text(surf, text, size, color, x, y, center=True, font_name=None):
    font = pygame.font.Font(font_name, size)
    txt = font.render(text, True, color)
    rect = txt.get_rect()
    if center:
        rect.center = (x, y)
    else:
        rect.topleft = (x, y)
    surf.blit(txt, rect)
    return rect

# --- CLASSES ---
class Nacho:
    def __init__(self, sprites):
        self.sprites = sprites
        self.state = NACHO_HAPPY
        self.x = WIDTH // 2
        self.y = HEIGHT - 80
        self.speed = 6
        self.base_speed = 6
        self.radius = 40
        self.warning_radius = 120
        self.danger_radius = 60
        self.width = sprites[0].get_width()
        self.height = sprites[0].get_height()
        self.lives = 3
        self.max_lives = 5
        self.invuln = 0
        self.shield = False
        self.smug_timer = 0
        self.crumble_anim = 0
        self.crumble_done = False

    def move(self, dx):
        if self.crumble_done:
            return
        self.x += dx * self.speed
        self.x = max(self.width//2, min(WIDTH-self.width//2, self.x))

    def update_state(self, hazards):
        if self.crumble_done:
            self.state = NACHO_CRACKED
            return
        if self.shield and self.smug_timer > 0:
            self.state = NACHO_CONFIDENT
            return
        min_dist = min((h.dist_to(self.x, self.y) for h in hazards if h.active), default=9999)
        if min_dist < self.danger_radius:
            self.state = NACHO_TERRIFIED
        elif min_dist < self.warning_radius:
            self.state = NACHO_ANXIOUS
        else:
            self.state = NACHO_HAPPY

    def draw(self, surf):
        if self.state == NACHO_CRACKED:
            idx = min(len(self.sprites)-1, 8 + self.crumble_anim)
        elif self.state == NACHO_CONFIDENT:
            idx = 6  # confident/smug
        elif self.state == NACHO_TERRIFIED:
            idx = random.choice([4,5])
        elif self.state == NACHO_ANXIOUS:
            idx = random.choice([2,3])
        else:
            idx = 0
        img = self.sprites[idx]
        surf.blit(img, (self.x - img.get_width()//2, self.y - img.get_height()//2))
        if self.shield:
            pygame.draw.circle(surf, (255, 223, 80), (self.x, self.y), self.width//2+8, 4)

    def hit(self):
        if self.shield:
            self.shield = False
            self.smug_timer = 0
            return False
        if self.invuln > 0:
            return False
        self.lives -= 1
        self.invuln = 60
        if self.lives <= 0:
            self.crumble_anim = 0
            self.crumble_done = False
            return True
        return False

    def update(self):
        if self.invuln > 0:
            self.invuln -= 1
        if self.smug_timer > 0:
            self.smug_timer -= 1
        if self.crumble_anim < 4 and self.state == NACHO_CRACKED:
            self.crumble_anim += 1
        if self.crumble_anim >= 4:
            self.crumble_done = True

    def give_life(self):
        if self.lives < self.max_lives:
            self.lives += 1

    def give_shield(self):
        self.shield = True
        self.smug_timer = 120

    def give_speed(self):
        self.speed = self.base_speed * 1.7
        pygame.time.set_timer(pygame.USEREVENT+2, 2000, True)

    def reset_speed(self):
        self.speed = self.base_speed

class Hazard:
    def __init__(self, kind, img, x, y, speed):
        self.kind = kind
        self.img = img
        self.x = x
        self.y = y
        self.speed = speed
        self.active = True
        self.ground_timer = 0
        self.dir = random.choice([-1,1])
        self.hitbox = img.get_rect(center=(x,y))

    def update(self):
        if not self.active:
            return
        if self.kind == 'olive' and self.ground_timer > 0:
            self.x += self.dir * 4
            self.ground_timer -= 1
            if self.ground_timer <= 0:
                self.active = False
        elif self.kind == 'sourcream' and self.ground_timer > 0:
            self.ground_timer -= 1
            if self.ground_timer <= 0:
                self.active = False
        else:
            self.y += self.speed
            if self.y > HEIGHT-60:
                if self.kind == 'olive':
                    self.ground_timer = 60
                elif self.kind == 'sourcream':
                    self.ground_timer = 120
                else:
                    self.active = False
        self.hitbox = self.img.get_rect(center=(self.x, self.y))

    def draw(self, surf):
        if not self.active:
            return
        surf.blit(self.img, (self.x - self.img.get_width()//2, self.y - self.img.get_height()//2))
        if self.kind == 'sourcream' and self.ground_timer > 0:
            pygame.draw.ellipse(surf, (255,255,255), (self.x-20, HEIGHT-40, 40, 12))

    def dist_to(self, px, py):
        return ((self.x-px)**2 + (self.y-py)**2)**0.5

    def collides(self, rect):
        return self.hitbox.colliderect(rect)

class PowerUp:
    def __init__(self, kind, img, x, y, speed):
        self.kind = kind
        self.img = img
        self.x = x
        self.y = y
        self.speed = speed
        self.active = True
        self.hitbox = img.get_rect(center=(x,y))

    def update(self):
        if not self.active:
            return
        self.y += self.speed
        if self.y > HEIGHT+40:
            self.active = False
        self.hitbox = self.img.get_rect(center=(self.x, self.y))

    def draw(self, surf):
        if not self.active:
            return
        surf.blit(self.img, (self.x - self.img.get_width()//2, self.y - self.img.get_height()//2))

    def collides(self, rect):
        return self.hitbox.colliderect(rect)

# --- MAIN GAME ---
def main():
    # Initialize pygame and set up the game window
    pygame.init()
    print("Starting game initialization...")
    
    # Set up the display
    flags = pygame.DOUBLEBUF | pygame.HWSURFACE
    screen = pygame.display.set_mode((WIDTH, HEIGHT), flags)
    pygame.display.set_caption("CRUNCH")
    print(f"Display created: {WIDTH}x{HEIGHT}")
    
    # Set up clock for controlling frame rate
    clock = pygame.time.Clock()
    print("Clock created")

    # Load font
    font_name = pygame.font.get_default_font()

    # Load assets
    print("Loading background image...")
    try:
        bg = pygame.image.load(os.path.join(ASSETS, 'table.png')).convert()
        bg = pygame.transform.scale(bg, (WIDTH, HEIGHT))
        print(f"Loaded background image: {bg.get_size()}")
    except Exception as e:
        print(f"Error loading background: {str(e)}")
        bg = pygame.Surface((WIDTH, HEIGHT))
        bg.fill((139, 69, 19))  # Brown fallback color

    print("Loading nacho sprites...")
    try:
        nacho_sprites, _, _ = load_sprite_sheet(os.path.join(ASSETS, 'squishnachos.png'), 5, 2)
        print(f"Loaded {len(nacho_sprites)} nacho sprites")
    except Exception as e:
        print(f"Error loading nacho sprites: {str(e)}")
        # Create a simple placeholder sprite if loading fails
        placeholder = pygame.Surface((64, 64), pygame.SRCALPHA)
        pygame.draw.circle(placeholder, (255, 200, 0), (32, 32), 30)
        nacho_sprites = [placeholder] * 10

    print("Loading hazard sprites...")
    try:
        hazard_sheet, _, _ = load_sprite_sheet(os.path.join(ASSETS, 'hazards.png'), 2, 3)
        print(f"Loaded {len(hazard_sheet)} hazard sprites")
    except Exception as e:
        print(f"Error loading hazard sprites: {str(e)}")
        hazard_sheet = [pygame.Surface((32, 32), pygame.SRCALPHA) for _ in range(6)]

    print("Loading powerup sprites...")
    try:
        powerup_sheet, _, _ = load_sprite_sheet(os.path.join(ASSETS, 'powerups.png'), 2, 3)
        print(f"Loaded {len(powerup_sheet)} powerup sprites")
    except Exception as e:
        print(f"Error loading powerup sprites: {str(e)}")
        powerup_sheet = [pygame.Surface((32, 32), pygame.SRCALPHA) for _ in range(6)]

    print("Loading health image...")
    try:
        health_img = pygame.image.load(os.path.join(ASSETS, 'health.png')).convert_alpha()
        print(f"Loaded health image: {health_img.get_size()}")
    except Exception as e:
        print(f"Error loading health image: {str(e)}")
        health_img = pygame.Surface((32, 32), pygame.SRCALPHA)
        pygame.draw.polygon(health_img, (255, 0, 0), [(16, 0), (32, 16), (16, 32), (0, 16)])

    # Map hazard sprites
    hazard_imgs = {
        'salsa': hazard_sheet[0],
        'guac': hazard_sheet[1],
        'jalapeno': hazard_sheet[2],
        'olive': hazard_sheet[3],
        'sourcream': hazard_sheet[4],
        'jar': hazard_sheet[5],
    }
    powerup_imgs = {
        'cheese': powerup_sheet[0],
        'lime': powerup_sheet[1],
        'kernel': powerup_sheet[2],
    }

    # Game state
    state = STATE_TITLE
    nacho = Nacho(nacho_sprites)
    hazards = []
    powerups = []
    lives = nacho.lives
    score = 0
    high_score = 0
    start_time = 0
    last_hazard = 0
    last_powerup = 0
    last_health = 0
    difficulty_timer = 0
    hazard_types = ['salsa']

    # Load high score
    try:
        with open('crunch_highscore.txt', 'r') as f:
            high_score = float(f.read())
    except:
        high_score = 0

    # Main game loop
    running = True

    while running:
        # Cap the frame rate and get delta time
        dt = clock.tick(FPS)

        # Process events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                print("Quit event received")
                running = False
            elif event.type == pygame.USEREVENT+2:
                print("Speed reset event")
                nacho.reset_speed()

            # Handle state-specific events
            if state == STATE_TITLE:
                if event.type == pygame.KEYDOWN and event.key in (pygame.K_RETURN, pygame.K_SPACE):
                    print("Starting new game")
                    state = STATE_PLAYING
                    nacho = Nacho(nacho_sprites)
                    hazards = []
                    powerups = []
                    score = 0
                    start_time = time.time()
                    last_hazard = 0
                    last_powerup = 0
                    last_health = 0
                    hazard_types = ['salsa']
                    difficulty_timer = 0
            elif state == STATE_GAMEOVER:
                if event.type == pygame.KEYDOWN and event.key in (pygame.K_RETURN, pygame.K_SPACE):
                    print("Returning to title screen")
                    state = STATE_TITLE

        # Handle key states
        keys = pygame.key.get_pressed()
        if state == STATE_PLAYING and not nacho.crumble_done:
            dx = 0
            if keys[pygame.K_LEFT] or keys[pygame.K_a]:
                dx -= 1
            if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
                dx += 1
            nacho.move(dx)

        # --- GAME LOGIC ---
        if state == STATE_PLAYING:
            now = time.time()
            score = now - start_time
            # Difficulty progression
            if score > 60 and 'olive' not in hazard_types:
                hazard_types += ['olive', 'sourcream']
            elif score > 30 and 'guac' not in hazard_types:
                hazard_types += ['guac', 'jalapeno']
            # Hazards
            if now - last_hazard > max(0.5, 2.0 - score/30):
                kind = random.choice(hazard_types)
                x = random.randint(40, WIDTH-40)
                speed = 3 + min(score/15, 7)
                hazards.append(Hazard(kind, hazard_imgs[kind], x, -40, speed))
                last_hazard = now
            # Powerups
            if now - last_powerup > random.uniform(7, 15):
                kind = random.choices(['cheese','lime','kernel'], weights=[0.5,0.3,0.2])[0]
                x = random.randint(40, WIDTH-40)
                speed = 3 + min(score/15, 7)
                powerups.append(PowerUp(kind, powerup_imgs[kind], x, -40, speed))
                last_powerup = now
            # Health
            if now - last_health > random.uniform(18, 30):
                x = random.randint(40, WIDTH-40)
                speed = 3 + min(score/15, 7)
                powerups.append(PowerUp('health', health_img, x, -40, speed))
                last_health = now
            # Update hazards
            for h in hazards:
                h.update()
            hazards = [h for h in hazards if h.active]
            # Update powerups
            for p in powerups:
                p.update()
            powerups = [p for p in powerups if p.active]
            # Nacho state
            nacho.update_state(hazards)
            nacho.update()
            # Collisions
            nacho_rect = pygame.Rect(nacho.x-nacho.width//2, nacho.y-nacho.height//2, nacho.width, nacho.height)
            for h in hazards:
                if h.active and h.collides(nacho_rect):
                    if h.kind == 'sourcream' and h.ground_timer > 0:
                        nacho.speed = nacho.base_speed * 0.5
                        pygame.time.set_timer(pygame.USEREVENT+2, 1200, True)
                        continue
                    h.active = False
                    if nacho.hit():
                        nacho.state = NACHO_CRACKED
                        state = STATE_GAMEOVER
                        if score > high_score:
                            high_score = score
                            with open('crunch_highscore.txt', 'w') as f:
                                f.write(str(high_score))
                        break
            for p in powerups:
                if p.active and p.collides(nacho_rect):
                    p.active = False
                    if p.kind == 'cheese':
                        nacho.give_shield()
                    elif p.kind == 'lime':
                        nacho.give_speed()
                    elif p.kind == 'kernel':
                        score += 10
                    elif p.kind == 'health':
                        nacho.give_life()

                # --- DRAW ---
                screen.fill(PALETTE[3])
                if state == STATE_TITLE:
                    screen.blit(bg, (0,0))
                    draw_text(screen, 'CRUNCH', 96, PALETTE[0], WIDTH//2, HEIGHT//3, font_name=font_name)
                    nacho.state = NACHO_HAPPY
                    nacho.draw(screen)
                    draw_text(screen, 'Press START', 40, PALETTE[1], WIDTH//2, HEIGHT//2+120, font_name=font_name)
                elif state == STATE_PLAYING:
                    screen.blit(bg, (0,0))
                    for h in hazards:
                        h.draw(screen)
                    for p in powerups:
                        p.draw(screen)
                    nacho.draw(screen)
                    # UI
                    for i in range(nacho.lives):
                        screen.blit(health_img, (20 + i*36, 20))
                    draw_text(screen, f'Score: {int(score)}', 32, PALETTE[2], WIDTH-120, 30, font_name=font_name)
                    draw_text(screen, f'High: {int(high_score)}', 24, PALETTE[1], WIDTH-120, 60, font_name=font_name)
                elif state == STATE_GAMEOVER:
                    screen.blit(bg, (0,0))
                    nacho.draw(screen)
                    draw_text(screen, 'CRUNCHED!', 80, PALETTE[0], WIDTH//2, HEIGHT//3, font_name=font_name)
                    draw_text(screen, f'Final Score: {int(score)}', 40, PALETTE[2], WIDTH//2, HEIGHT//2, font_name=font_name)
                    draw_text(screen, f'High Score: {int(high_score)}', 32, PALETTE[1], WIDTH//2, HEIGHT//2+50, font_name=font_name)
                    draw_text(screen, 'Try Again?', 36, PALETTE[1], WIDTH//2, HEIGHT//2+120, font_name=font_name)
                pygame.display.flip()
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        pygame.quit()
        print("Game exited cleanly")
        sys.exit()

if __name__ == '__main__':
    main()