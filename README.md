# CRUNCH - Nacho Survival Game

A fun arcade-style game where you control a nacho character avoiding falling hazards and collecting power-ups!

## Installation & Setup

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Step 1: Install pygame
Open a command prompt/terminal and run:

```bash
pip install pygame
```

Or install from requirements.txt:
```bash
pip install -r requirements.txt
```

### Step 2: Verify Installation
Test if pygame is working:
```bash
python test_python.py
```

### Step 3: Run the Game
```bash
python main.py
```

Or double-click `run_game.bat` on Windows.

## Game Controls
- **Arrow Keys** or **WASD**: Move the nacho left/right
- **Enter/Space**: Start game or restart after game over
- **Escape**: Quit game

## Game Features
- **Nacho Character**: Expressive nacho with different emotional states
- **Hazards**: Avoid falling salsa, guac, jalapeños, olives, and sour cream
- **Power-ups**: 
  - 🧀 Cheese: Temporary shield
  - 🍋 Lime: Speed boost
  - 🌽 Kernel: Bonus points
  - ❤️ Health: Extra life
- **Progressive Difficulty**: More hazard types unlock as you survive longer
- **High Score System**: Tracks your best survival time

## Troubleshooting

### Game window doesn't appear
1. Make sure pygame is installed: `pip install pygame`
2. Check if you have a display/graphics driver
3. Try running `python test_pygame.py` to diagnose issues

### Game freezes on startup
1. Update graphics drivers
2. Try running in compatibility mode
3. Check if antivirus is blocking the game

### Import errors
1. Reinstall pygame: `pip uninstall pygame` then `pip install pygame`
2. Make sure you're using the correct Python version
3. Try: `python -m pip install pygame`

### Performance issues
1. Close other applications
2. Update graphics drivers
3. Lower the FPS in the code if needed

## File Structure
- `main.py` - Main game file
- `assets/` - Game sprites and images
- `test_python.py` - Python environment test
- `test_pygame.py` - Pygame installation test
- `run_game.bat` - Windows batch file to run the game
- `requirements.txt` - Python dependencies

## Assets
All required game assets are included in the `assets/` folder:
- `squishnachos.png` - Nacho character sprites
- `hazards.png` - Falling hazard sprites  
- `powerups.png` - Power-up sprites
- `health.png` - Health/life icon
- `table.png` - Background image

## Development
The game is built with Python and pygame. Key components:
- **Nacho class**: Player character with states and movement
- **Hazard class**: Falling objects with different behaviors
- **PowerUp class**: Collectible items with various effects
- **Game states**: Title screen, playing, and game over

Enjoy playing CRUNCH! 🌮
