@echo off
echo ========================================
echo PYGAME INSTALLATION SCRIPT
echo ========================================
echo.
echo Checking Python installation...
python --version
echo.
echo Installing pygame...
pip install pygame
echo.
echo Verifying pygame installation...
python -c "import pygame; print('SUCCESS: Pygame version', pygame.version.ver, 'installed correctly!')"
echo.
if %errorlevel% equ 0 (
    echo ✓ Pygame installed successfully!
    echo You can now run the game with: python main.py
) else (
    echo ✗ Pygame installation failed. Trying alternative method...
    pip install pygame --user
    python -c "import pygame; print('SUCCESS: Pygame version', pygame.version.ver, 'installed correctly!')"
)
echo.
echo Installation complete. Press any key to exit...
pause
