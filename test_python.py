#!/usr/bin/env python3
"""
Simple Python test to check if Python is working
"""
import sys
import os

print("=" * 50)
print("PYTHON ENVIRONMENT TEST")
print("=" * 50)
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")
print(f"Platform: {sys.platform}")

print("\nTesting basic imports...")
try:
    import random
    print("✓ random module imported")
except ImportError as e:
    print(f"✗ Failed to import random: {e}")

try:
    import time
    print("✓ time module imported")
except ImportError as e:
    print(f"✗ Failed to import time: {e}")

try:
    import os
    print("✓ os module imported")
except ImportError as e:
    print(f"✗ Failed to import os: {e}")

print("\nTesting pygame import...")
try:
    import pygame
    print(f"✓ pygame imported successfully - version {pygame.version.ver}")
except ImportError as e:
    print(f"✗ Failed to import pygame: {e}")
    print("Please install pygame with: pip install pygame")

print("\nTest completed!")
print("=" * 50)
