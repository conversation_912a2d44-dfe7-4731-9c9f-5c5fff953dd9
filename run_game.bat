@echo off
echo Starting CRUNCH game...
echo.
echo Checking Python installation...
python --version
echo.
echo Installing/updating pygame...
pip install pygame
echo.
echo Checking pygame installation...
python -c "import pygame; print('Pygame version:', pygame.version.ver)" 2>nul
if %errorlevel% neq 0 (
    echo Failed to import pygame. Trying alternative installation...
    pip install pygame --upgrade --force-reinstall
    python -c "import pygame; print('Pygame version:', pygame.version.ver)"
)
echo.
echo Starting game...
python main.py
echo.
echo Game finished. Press any key to exit...
pause
